{"easycom": {"autoscan": true, "custom": {"^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue"}}, "condition": {"current": 0, "list": [{"name": "chat", "path": "pages/chat/chat"}]}, "pages": [{"path": "pages/face/face", "style": {"navigationBarTitleText": "名片列表"}}, {"path": "pages/index/index", "style": {"navigationBarTitleText": "名片列表"}}, {"path": "pages/cardDetail/cardDetail", "style": {"navigationBarTitleText": "我的名片", "navigationStyle": "custom"}}, {"path": "pages/chat/chat", "style": {"navigationBarTitleText": "我的名片"}}, {"path": "pages/wot-test/wot-test", "style": {"navigationBarTitleText": "Wot-UI 测试"}}], "globalStyle": {"navigationBarTextStyle": "black", "navigationBarTitleText": "EaseGYM", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8"}, "uniIdRouter": {}}