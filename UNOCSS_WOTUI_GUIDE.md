# 原子化 CSS + Wot-UI 使用指南

本项目已集成自定义原子化 CSS 和 Wot-UI，为开发提供强大的原子化样式和组件库支持。

## 已安装的依赖

### Wot-UI
- `wot-design-uni` - 适用于 uni-app 的组件库

### 原子化 CSS
- `static/atomic.css` - 自定义原子化 CSS 文件，提供类似 Tailwind CSS 的功能

## 配置文件

### static/atomic.css
自定义原子化 CSS 文件，包含：
- 布局相关类（flex、grid 等）
- 间距类（padding、margin）
- 文本样式类
- 颜色类
- 边框和圆角类
- 阴影类
- 其他实用类

### pages.json
配置了 wot-ui 的 easycom 自动导入：
```json
{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue"
    }
  }
}
```

## 使用示例

### 原子化 CSS
```vue
<template>
  <!-- 布局类 -->
  <view class="flex items-center justify-center p-4">
    <!-- 文本样式 -->
    <text class="text-2xl font-bold text-blue-600">标题</text>
  </view>

  <!-- 背景和边框 -->
  <view class="bg-white rounded-lg shadow-md p-6">
    内容区域
  </view>

  <!-- 宽度控制 -->
  <view class="w-full">
    全宽容器
  </view>
  <view class="w-1/2">
    半宽容器
  </view>
</template>
```

### Wot-UI 组件
```vue
<template>
  <!-- 按钮组件 -->
  <wd-button type="primary">主要按钮</wd-button>
  <wd-button type="success">成功按钮</wd-button>
  
  <!-- 输入框 -->
  <wd-input v-model="inputValue" placeholder="请输入内容" />
  
  <!-- 标签 -->
  <wd-tag type="primary">标签</wd-tag>
  
  <!-- 单元格 -->
  <wd-cell title="标题" value="内容" />
</template>

<script setup>
import { ref } from 'vue'
const inputValue = ref('')
</script>
```

## 常用原子化类

项目中提供了丰富的原子化 CSS 类：

### 布局类
- `flex` - 弹性布局
- `flex-center` - 居中布局
- `flex-between` - 两端对齐
- `flex-col-center` - 垂直居中

### 间距类
- `p-1` 到 `p-8` - 内边距
- `m-1` 到 `m-6` - 外边距
- `px-*`, `py-*`, `mx-*`, `my-*` - 方向性间距

### 文本类
- `text-xs` 到 `text-3xl` - 字体大小
- `font-normal`, `font-medium`, `font-semibold`, `font-bold` - 字体粗细
- `text-center`, `text-left`, `text-right` - 文本对齐
- `text-ellipsis` - 文本省略

### 颜色类
- `text-*` - 文本颜色
- `bg-*` - 背景颜色

### 边框和圆角
- `rounded`, `rounded-lg`, `rounded-xl` - 圆角
- `border`, `border-2` - 边框

### 阴影
- `shadow`, `shadow-md`, `shadow-lg` - 阴影效果

## 测试页面

项目中包含了一个专门的测试页面 `pages/wot-test/wot-test.vue`，用于测试 wot-ui 组件的功能。您可以通过主页面的"测试 Wot-UI 组件"按钮访问。

## 注意事项

1. **小程序兼容性**：所有 CSS 类都经过小程序兼容性测试
2. **组件自动导入**：wot-ui 组件通过 easycom 配置自动导入，无需手动引入
3. **样式优先级**：原子化类优先级较高，可以覆盖大部分默认样式
4. **开发建议**：优先使用原子化类，复杂样式可以通过组合实现或使用内联样式
5. **wot-ui 使用**：如果 wot-ui 组件无法正常显示，请检查 easycom 配置是否正确

## 更多资源

- [Wot-UI 官方文档](https://wot-design-uni.cn/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)（参考语法）
