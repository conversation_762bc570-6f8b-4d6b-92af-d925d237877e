# UnoCSS + Wot-UI 使用指南

本项目已集成 UnoCSS 和 Wot-UI，为开发提供强大的原子化 CSS 和组件库支持。

## 已安装的依赖

### UnoCSS 相关
- `unocss` - 核心库
- `@unocss/preset-uno` - 默认预设
- `@unocss/preset-attributify` - 属性化预设
- `@unocss/preset-icons` - 图标预设
- `@unocss/transformer-directives` - 指令转换器
- `@unocss/transformer-variant-group` - 变体组转换器

### Wot-UI
- `wot-design-uni` - 适用于 uni-app 的组件库

## 配置文件

### uno.config.js
UnoCSS 的配置文件，包含：
- 预设配置
- 自定义快捷类
- 主题配置
- 小程序安全列表

### vite.config.js
Vite 配置文件，集成了 UnoCSS 插件

### pages.json
配置了 wot-ui 的 easycom 自动导入：
```json
{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue"
    }
  }
}
```

## 使用示例

### UnoCSS 原子化 CSS
```vue
<template>
  <!-- 布局类 -->
  <view class="flex items-center justify-center p-4">
    <!-- 文本样式 -->
    <text class="text-2xl font-bold text-blue-600">标题</text>
  </view>
  
  <!-- 背景和边框 -->
  <view class="bg-white rounded-lg shadow-md p-6">
    内容区域
  </view>
  
  <!-- 响应式设计 -->
  <view class="w-full md:w-1/2 lg:w-1/3">
    响应式容器
  </view>
</template>
```

### Wot-UI 组件
```vue
<template>
  <!-- 按钮组件 -->
  <wd-button type="primary">主要按钮</wd-button>
  <wd-button type="success">成功按钮</wd-button>
  
  <!-- 输入框 -->
  <wd-input v-model="inputValue" placeholder="请输入内容" />
  
  <!-- 标签 -->
  <wd-tag type="primary">标签</wd-tag>
  
  <!-- 单元格 -->
  <wd-cell title="标题" value="内容" />
</template>

<script setup>
import { ref } from 'vue'
const inputValue = ref('')
</script>
```

## 自定义快捷类

项目中预定义了一些常用的快捷类：

- `btn` - 基础按钮样式
- `btn-primary` - 主要按钮
- `btn-secondary` - 次要按钮
- `icon-btn` - 图标按钮
- `flex-center` - 居中布局
- `flex-between` - 两端对齐
- `flex-col-center` - 垂直居中
- `text-ellipsis` - 文本省略

## 主题配置

在 `uno.config.js` 中定义了主题色：
- `primary`: #007aff
- `success`: #4cd964
- `warning`: #f0ad4e
- `error`: #dd524d

## 注意事项

1. **小程序兼容性**：UnoCSS 配置中包含了小程序的安全列表，确保常用类名不被过滤
2. **组件自动导入**：wot-ui 组件已配置自动导入，无需手动引入
3. **样式优先级**：UnoCSS 的原子化类优先级较高，可以覆盖大部分默认样式
4. **开发建议**：优先使用 UnoCSS 的原子化类，复杂样式可以通过组合实现

## 更多资源

- [UnoCSS 官方文档](https://unocss.dev/)
- [Wot-UI 官方文档](https://wot-design-uni.cn/)
- [UnoCSS 交互式文档](https://unocss.dev/interactive/)
