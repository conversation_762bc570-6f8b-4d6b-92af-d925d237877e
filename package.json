{"name": "business-card-mini-program", "version": "1.0.0", "description": "名片小程序", "main": "main.js", "scripts": {"lint": "eslint --ext .js,.vue .", "lint:fix": "eslint --ext .js,.vue . --fix", "format": "prettier --write \"**/*.{js,vue,json,css,scss,html,md}\""}, "keywords": ["uni-app", "vue3", "miniprogram", "business-card"], "author": "", "license": "MIT", "devDependencies": {"@dcloudio/vite-plugin-uni": "3.0.0-alpha-3000020210521001", "@unocss/preset-attributify": "^66.4.2", "@unocss/preset-icons": "^66.4.2", "@unocss/preset-uno": "^66.4.2", "@unocss/transformer-directives": "^66.4.2", "@unocss/transformer-variant-group": "^66.4.2", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-vue": "^10.4.0", "prettier": "^3.6.2", "unocss": "^66.4.2", "vite": "^7.1.3"}, "dependencies": {"pinia": "^2.1.7", "wot-design-uni": "^1.12.2"}}