<template>
  <view class="p-4 w-full box-border min-h-screen bg-gray-50">
    <!-- 原子化 CSS + wot-ui 示例区域 -->
    <view class="mb-6">
      <text class="text-2xl font-bold text-gray-800 mb-4 block">原子化 CSS + Wot-UI 示例</text>

      <!-- wot-ui 按钮示例 -->
      <view class="mb-4">
        <wd-button type="primary" class="mr-2">主要按钮</wd-button>
        <wd-button type="success" class="mr-2">成功按钮</wd-button>
        <wd-button type="warning">警告按钮</wd-button>
      </view>

      <!-- 原子化 CSS 样式示例 -->
      <view class="flex-center bg-blue-100 p-4 rounded-lg mb-4">
        <text class="text-blue-600 font-medium">原子化 CSS 居中布局示例</text>
      </view>

      <!-- wot-ui 输入框示例 -->
      <wd-input v-model="searchText" placeholder="搜索名片..." class="mb-4" />

      <!-- wot-ui 标签示例 -->
      <view class="mb-4">
        <wd-tag type="primary" class="mr-2">标签1</wd-tag>
        <wd-tag type="success" class="mr-2">标签2</wd-tag>
        <wd-tag type="warning">标签3</wd-tag>
      </view>
    </view>

    <!-- 原有的名片列表 -->
    <view class="mb-4">
      <text class="text-xl font-semibold text-gray-700 mb-3 block">名片列表</text>
    </view>

    <view
      v-for="(item, index) in filteredList"
      :key="index"
      class="w-full mb-4 cursor-pointer"
      @tap="onOpencard(item)"
    >
      <view class="w-full img-box rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
        <view class="absolute inset-0">
          <image :src="item.picture" mode="aspectFill" class="w-full h-full"></image>
        </view>
        <!-- 覆盖层 -->
        <view class="absolute bottom-0 left-0 right-0 p-4" style="background: linear-gradient(to top, rgba(0,0,0,0.6), transparent);">
          <text class="text-white font-medium text-lg">{{ item.name || '名片' }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
// import {onShow} from "@dcloudio/uni-app"
import mockJson from '@/api/mock-card.json'

const list = ref(mockJson)
const searchText = ref('')

// 过滤后的列表
const filteredList = computed(() => {
  if (!searchText.value) {
    return list.value
  }
  return list.value.filter(item =>
    (item.name && item.name.includes(searchText.value)) ||
    (item.title && item.title.includes(searchText.value))
  )
})

// onShow(()=>{
// 	uni.showLoading({
// 		mask:true,
// 	})
// 	setTimeout(()=>{
// 		uni.hideLoading()
// 	},1000)
// })

function onOpencard(item) {
  uni.navigateTo({
    url: `/pages/cardDetail/cardDetail?id=${item.id}`,
  })
}
</script>

<style lang="scss" scoped>
.img-box {
  height: 0;
  padding-bottom: 133.33%;
  position: relative;
}

/* UnoCSS 会自动处理大部分样式，这里只保留必要的自定义样式 */
</style>
