<template>
  <view class="p-4">
    <text class="text-2xl font-bold mb-4 block">Wot-UI 组件测试</text>
    
    <!-- 测试按钮 -->
    <view class="mb-4">
      <text class="text-lg font-medium mb-2 block">按钮组件</text>
      <wd-button type="primary" class="mr-2">主要按钮</wd-button>
      <wd-button type="success">成功按钮</wd-button>
    </view>
    
    <!-- 测试输入框 -->
    <view class="mb-4">
      <text class="text-lg font-medium mb-2 block">输入框组件</text>
      <wd-input v-model="inputValue" placeholder="请输入内容" />
    </view>
    
    <!-- 测试标签 -->
    <view class="mb-4">
      <text class="text-lg font-medium mb-2 block">标签组件</text>
      <wd-tag type="primary" class="mr-2">主要</wd-tag>
      <wd-tag type="success" class="mr-2">成功</wd-tag>
      <wd-tag type="warning">警告</wd-tag>
    </view>
    
    <!-- 测试单元格 -->
    <view class="mb-4">
      <text class="text-lg font-medium mb-2 block">单元格组件</text>
      <wd-cell title="标题" value="内容" />
      <wd-cell title="带图标" value="内容" icon="setting" />
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

const inputValue = ref('')
</script>

<style lang="scss" scoped>
/* 使用原子化 CSS，这里只保留必要的自定义样式 */
</style>
